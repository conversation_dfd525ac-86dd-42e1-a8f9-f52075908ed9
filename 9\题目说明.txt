第四道题目：数值方法求解微分方程稳定性可视化分析

图片内容：代码文件9A.py生成的数值方法比较可视化结果（显式欧拉、隐式欧拉、四阶R-K、SciPy ODE方法的解曲线对比，以及稳定性区域分析）

问题：观察上述数值方法求解刚性微分方程的可视化结果，当步长h=0.2时，哪种说法是正确的？

A. 显式欧拉方法保持稳定，四阶龙格-库塔方法出现振荡
B. 隐式欧拉方法最稳定，显式欧拉和四阶R-K方法都不稳定  
C. 所有方法都保持稳定，但隐式欧拉精度最高
D. 四阶龙格-库塔方法最精确，但显式欧拉方法最稳定

答案：B

推理过程：
1）微分方程dy/dt = -10y + sin(t)是刚性方程，特征值λ = -10；
2）对于h=0.2，hλ = -2，这个值落在显式欧拉稳定区域（|1+hλ|≤1，即|1-2|=1）的边界上，实际会不稳定；
3）四阶R-K方法的稳定区域虽然更大，但hλ=-2仍可能超出其稳定区域；
4）隐式欧拉方法具有无条件稳定性（A-稳定），对任何负实数hλ都稳定。

特点：
- 新颖度：刚性微分方程的数值稳定性分析，结合理论与实践
- 难度：需要理解不同数值方法的稳定性区域和刚性方程的特点
- 必须看图：需要观察不同方法的解曲线稳定性和稳定性区域图 