import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint
import warnings

# 题目：分析不同数值方法求解微分方程的稳定性可视化结果

def differential_equation(y, t):
    """
    定义微分方程: dy/dt = -λy + sin(t)
    这是一个线性非齐次微分方程
    解析解: y(t) = (y0 + 1/(1+λ²))e^(-λt) + (sin(t) - λcos(t))/(1+λ²)
    """
    lambda_val = 10.0  # 刚性参数
    return -lambda_val * y + np.sin(t)

def analytical_solution(t, y0):
    """解析解"""
    lambda_val = 10.0
    term1 = (y0 + 1/(1 + lambda_val**2)) * np.exp(-lambda_val * t)
    term2 = (np.sin(t) - lambda_val * np.cos(t)) / (1 + lambda_val**2)
    return term1 + term2

def euler_method(func, y0, t_span, h):
    """显式欧拉方法"""
    t = np.arange(t_span[0], t_span[1] + h, h)
    y = np.zeros(len(t))
    y[0] = y0
    
    for i in range(1, len(t)):
        y[i] = y[i-1] + h * func(y[i-1], t[i-1])
        
        # 检查数值不稳定
        if abs(y[i]) > 1e10:
            y[i:] = np.nan
            break
            
    return t, y

def implicit_euler(func, y0, t_span, h):
    """隐式欧拉方法（简化版，适用于线性情况）"""
    lambda_val = 10.0
    t = np.arange(t_span[0], t_span[1] + h, h)
    y = np.zeros(len(t))
    y[0] = y0
    
    for i in range(1, len(t)):
        # 对于 dy/dt = -λy + sin(t)，隐式欧拉：
        # y[i] = y[i-1] + h*(-λ*y[i] + sin(t[i]))
        # y[i] = (y[i-1] + h*sin(t[i])) / (1 + h*λ)
        y[i] = (y[i-1] + h * np.sin(t[i])) / (1 + h * lambda_val)
        
    return t, y

def runge_kutta_4(func, y0, t_span, h):
    """四阶龙格-库塔方法"""
    t = np.arange(t_span[0], t_span[1] + h, h)
    y = np.zeros(len(t))
    y[0] = y0
    
    for i in range(1, len(t)):
        k1 = h * func(y[i-1], t[i-1])
        k2 = h * func(y[i-1] + k1/2, t[i-1] + h/2)
        k3 = h * func(y[i-1] + k2/2, t[i-1] + h/2)
        k4 = h * func(y[i-1] + k3, t[i-1] + h)
        
        y[i] = y[i-1] + (k1 + 2*k2 + 2*k3 + k4) / 6
        
        # 检查数值不稳定
        if abs(y[i]) > 1e10:
            y[i:] = np.nan
            break
            
    return t, y

def analyze_numerical_methods():
    """分析不同数值方法的稳定性"""
    y0 = 1.0  # 初始条件
    t_span = [0, 2]  # 时间范围
    
    # 不同的步长
    step_sizes = [0.1, 0.15, 0.2, 0.25]
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    colors = ['blue', 'red', 'green', 'orange']
    methods = [
        ('显式欧拉', euler_method),
        ('隐式欧拉', implicit_euler),
        ('四阶R-K', runge_kutta_4),
        ('SciPy ODE', lambda func, y0, t_span, h: 
         (np.linspace(t_span[0], t_span[1], int((t_span[1]-t_span[0])/h) + 1),
          odeint(func, y0, np.linspace(t_span[0], t_span[1], int((t_span[1]-t_span[0])/h) + 1)).flatten()))
    ]
    
    # 解析解
    t_analytical = np.linspace(t_span[0], t_span[1], 1000)
    y_analytical = analytical_solution(t_analytical, y0)
    
    for idx, (method_name, method_func) in enumerate(methods):
        ax = axes[idx]
        
        # 绘制解析解
        ax.plot(t_analytical, y_analytical, 'k--', linewidth=2, 
                label='解析解', alpha=0.8)
        
        # 测试不同步长
        for i, h in enumerate(step_sizes):
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    t_num, y_num = method_func(differential_equation, y0, t_span, h)
                
                # 处理NaN值
                valid_mask = ~np.isnan(y_num)
                if np.sum(valid_mask) > 1:
                    ax.plot(t_num[valid_mask], y_num[valid_mask], 
                           color=colors[i], marker='o', markersize=3,
                           label=f'h={h}', alpha=0.7)
                else:
                    # 如果全是NaN，标记为不稳定
                    ax.text(0.5, 0.8 - i*0.1, f'h={h}: 不稳定', 
                           transform=ax.transAxes, color=colors[i], 
                           fontweight='bold')
                    
            except Exception as e:
                ax.text(0.5, 0.8 - i*0.1, f'h={h}: 计算失败', 
                       transform=ax.transAxes, color=colors[i])
        
        ax.set_xlabel('时间 t')
        ax.set_ylabel('y(t)')
        ax.set_title(f'{method_name}方法')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_ylim(-2, 5)  # 限制y轴范围以便观察
    
    plt.tight_layout()
    plt.show()
    
    # 稳定性分析
    analyze_stability_regions()

def analyze_stability_regions():
    """分析各方法的稳定性区域"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # 创建复平面网格
    real = np.linspace(-4, 2, 300)
    imag = np.linspace(-3, 3, 300)
    R, I = np.meshgrid(real, imag)
    z = R + 1j * I
    
    methods_stability = [
        ('显式欧拉', lambda z: np.abs(1 + z) <= 1),
        ('隐式欧拉', lambda z: np.abs(1 / (1 - z)) <= 1),
        ('四阶R-K', lambda z: np.abs(1 + z + z**2/2 + z**3/6 + z**4/24) <= 1)
    ]
    
    for idx, (method_name, stability_func) in enumerate(methods_stability):
        ax = axes[idx]
        
        # 计算稳定性区域
        stable = stability_func(z)
        
        # 绘制稳定性区域
        ax.contourf(R, I, stable.astype(int), levels=[0.5, 1.5], 
                   colors=['lightblue'], alpha=0.7)
        ax.contour(R, I, stable.astype(int), levels=[0.5], 
                  colors=['blue'], linewidths=2)
        
        # 标记特殊点
        lambda_val = -10.0  # 我们微分方程的特征值
        step_sizes = [0.1, 0.15, 0.2, 0.25]
        
        for h in step_sizes:
            point = h * lambda_val
            color = 'red' if not stability_func(np.array([point]))[0] else 'green'
            ax.plot(point.real, point.imag, 'o', color=color, markersize=8,
                   label=f'hλ, h={h}')
        
        ax.set_xlabel('实部 Re(hλ)')
        ax.set_ylabel('虚部 Im(hλ)')
        ax.set_title(f'{method_name}稳定性区域')
        ax.grid(True, alpha=0.3)
        ax.legend()
        ax.set_xlim(-4, 2)
        ax.set_ylim(-3, 3)
    
    plt.tight_layout()
    plt.show()

# 运行分析
if __name__ == "__main__":
    analyze_numerical_methods()

"""
问题：观察上述数值方法求解刚性微分方程的可视化结果，
当步长h=0.2时，哪种说法是正确的？

A. 显式欧拉方法保持稳定，四阶龙格-库塔方法出现振荡
B. 隐式欧拉方法最稳定，显式欧拉和四阶R-K方法都不稳定  
C. 所有方法都保持稳定，但隐式欧拉精度最高
D. 四阶龙格-库塔方法最精确，但显式欧拉方法最稳定

答案：B

推理过程：
1）微分方程dy/dt = -10y + sin(t)是刚性方程，特征值λ = -10；
2）对于h=0.2，hλ = -2，这个值落在显式欧拉稳定区域（|1+hλ|≤1，即|1-2|=1）的边界上，实际会不稳定；
3）四阶R-K方法的稳定区域虽然更大，但hλ=-2仍可能超出其稳定区域；
4）隐式欧拉方法具有无条件稳定性（A-稳定），对任何负实数hλ都稳定。
""" 